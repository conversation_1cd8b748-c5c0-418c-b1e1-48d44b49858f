-- Add assigned_user_id field to cases table
-- This migration adds the ability to assign users to cases

-- Add assigned_user_id column to cases table
ALTER TABLE public.cases 
ADD COLUMN IF NOT EXISTS assigned_user_id UUID REFERENCES auth.users(id);

-- Create index for better performance on assigned user queries
CREATE INDEX IF NOT EXISTS idx_cases_assigned_user_id ON public.cases(assigned_user_id);

-- Update the Case interface type to include assigned user information
-- This will be handled in the TypeScript code

-- Add comment to document the field
COMMENT ON COLUMN public.cases.assigned_user_id IS 'User assigned to handle this case';
