import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface GetCompanyUsersRequest {
  companyId: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { companyId }: GetCompanyUsersRequest = await req.json();

    if (!companyId) {
      return new Response(
        JSON.stringify({ success: false, error: 'companyId is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Fetching users for company:', companyId);

    // Get user roles for the company
    const { data: userRoles, error: rolesError } = await supabaseAdmin
      .from('user_roles')
      .select('user_id, role, created_at')
      .eq('company_id', companyId);

    if (rolesError) {
      console.error('Error fetching user roles:', rolesError);
      throw new Error('Failed to fetch user roles: ' + rolesError.message);
    }

    if (!userRoles || userRoles.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          users: []
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get auth user details for each user
    const userIds = userRoles.map(role => role.user_id);
    const users = [];

    for (const userId of userIds) {
      try {
        const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(userId);
        
        if (authError) {
          console.warn(`Failed to get auth user ${userId}:`, authError);
          continue;
        }

        if (authUser?.user) {
          const userRole = userRoles.find(role => role.user_id === userId);
          
          users.push({
            id: authUser.user.id,
            email: authUser.user.email,
            full_name: authUser.user.user_metadata?.full_name || '',
            phone: authUser.user.user_metadata?.phone || authUser.user.phone || '',
            role: userRole?.role || 'user',
            created_at: userRole?.created_at || authUser.user.created_at,
            last_sign_in_at: authUser.user.last_sign_in_at,
            is_active: true, // Assume active if user exists in auth
            status: 'active' // Default status
          });
        }
      } catch (error) {
        console.warn(`Error processing user ${userId}:`, error);
        continue;
      }
    }

    console.log(`Successfully fetched ${users.length} users for company ${companyId}`);

    return new Response(
      JSON.stringify({
        success: true,
        users: users
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('Error in get-company-users:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

serve(handler);
