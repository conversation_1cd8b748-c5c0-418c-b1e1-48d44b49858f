import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from './useAuth';
import { toast } from 'sonner';

export interface CompanyUser {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'company_admin' | 'user' | 'super_admin';
  created_at: string;
  last_sign_in_at?: string;
  is_active: boolean;
  status?: 'active' | 'pending' | 'expired';
  invitation_id?: string;
}

export interface CreateUserData {
  email: string;
  full_name: string;
  phone?: string;
  role: 'company_admin' | 'user';
  password: string;
}

export const useUserManagement = () => {
  const [users, setUsers] = useState<CompanyUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const { currentCompany } = useCompany();
  const { user: currentUser, isSuperAdmin } = useAuth();

  // Fetch users for the current company
  const fetchUsers = async () => {
    if (!currentCompany?.id) return;

    try {
      setIsLoading(true);

      // Use the new get-company-users function to get real user data
      const { data: result, error: fetchError } = await supabase.functions.invoke('get-company-users', {
        body: {
          companyId: currentCompany.id
        }
      });

      if (fetchError) throw fetchError;

      if (!result?.success) {
        throw new Error(result?.error || 'Failed to fetch users');
      }

      // Transform the data to match our interface
      const transformedUsers: CompanyUser[] = result.users?.map((user: any) => ({
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        phone: user.phone,
        role: user.role as 'company_admin' | 'user' | 'super_admin',
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
        is_active: user.is_active,
        status: user.status as 'active' | 'pending' | 'expired'
      })) || [];

      // Only show active users (no more invitations)
      setUsers(transformedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('שגיאה בטעינת המשתמשים');
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new user directly (only for super admins)
  const createUser = async (userData: CreateUserData) => {
    if (!currentCompany?.id || !currentUser?.id) {
      toast.error('שגיאה: לא נמצאה חברה או משתמש פעיל');
      return false;
    }

    try {
      setIsCreating(true);

      // Generate a temporary password if none provided
      const password = userData.password || `Temp${Math.random().toString(36).slice(-8)}!`;

      // Use the create-user-with-password function to create user directly
      const { data: createUserResult, error: createUserError } = await supabase.functions.invoke('create-user-with-password', {
        body: {
          email: userData.email,
          password: password,
          fullName: userData.full_name,
          phone: userData.phone,
          role: userData.role,
          companyId: currentCompany.id
        }
      });

      if (createUserError) throw createUserError;

      if (!createUserResult?.success) {
        throw new Error(createUserResult?.error || 'Failed to create user');
      }

      toast.success(`משתמש נוצר בהצלחה! סיסמה זמנית: ${password}`);
      await fetchUsers(); // Refresh the list
      return true;

    } catch (error: any) {
      console.error('Error creating user:', error);
      if (error.message?.includes('already registered') || error.message?.includes('already exists')) {
        toast.error('משתמש עם כתובת אימייל זו כבר קיים');
      } else {
        toast.error('שגיאה ביצירת המשתמש');
      }
      return false;
    } finally {
      setIsCreating(false);
    }
  };

  // Update user role
  const updateUserRole = async (userId: string, newRole: 'company_admin' | 'user') => {
    if (!currentCompany?.id) return false;

    try {
      const { error } = await supabase
        .from('user_roles')
        .update({ role: newRole })
        .eq('user_id', userId)
        .eq('company_id', currentCompany.id);

      if (error) throw error;

      toast.success('תפקיד המשתמש עודכן בהצלחה');
      await fetchUsers(); // Refresh the list
      return true;

    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('שגיאה בעדכון תפקיד המשתמש');
      return false;
    }
  };

  // Deactivate user (remove from company completely)
  const deactivateUser = async (userId: string) => {
    if (!currentCompany?.id) return false;

    try {
      // Use the new user-management function to delete completely
      const { data: deleteResult, error: deleteError } = await supabase.functions.invoke('user-management', {
        body: {
          action: 'delete',
          userId: userId
        }
      });

      if (deleteError) throw deleteError;

      if (!deleteResult?.success) {
        throw new Error(deleteResult?.error || 'Failed to delete user');
      }

      toast.success('המשתמש נמחק לחלוטין מהמערכת');
      await fetchUsers(); // Refresh the list
      return true;

    } catch (error) {
      console.error('Error deactivating user:', error);
      toast.error('שגיאה בהסרת המשתמש');
      return false;
    }
  };

  // Send password reset email
  const sendPasswordReset = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) throw error;

      toast.success('נשלח אימייל לאיפוס סיסמה');
      return true;

    } catch (error) {
      console.error('Error sending password reset:', error);
      toast.error('שגיאה בשליחת אימייל לאיפוס סיסמה');
      return false;
    }
  };

  // Check if current user can manage users (company_admin or super_admin)
  const canManageUsers = () => {
    // Super admin can manage users in any company
    if (isSuperAdmin) {
      return true;
    }

    // Company admin can manage users in their own company
    const currentUserRole = users.find(u => u.id === currentUser?.id)?.role;
    return currentUserRole === 'company_admin';
  };

  // Get user statistics
  const getUserStats = () => {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.is_active).length;
    const adminUsers = users.filter(u => u.role === 'company_admin' || u.role === 'super_admin').length;
    const regularUsers = users.filter(u => u.role === 'user').length;

    return {
      total: totalUsers,
      active: activeUsers,
      admins: adminUsers,
      users: regularUsers
    };
  };

  useEffect(() => {
    fetchUsers();
  }, [currentCompany?.id]);

  // Update user details (name, email, phone)
  const updateUser = async (userId: string, updates: { full_name?: string; email?: string; phone?: string }) => {
    if (!currentCompany?.id) return false;

    try {
      // Use the update-user-metadata function to update user details
      const { data: updateResult, error: updateError } = await supabase.functions.invoke('update-user-metadata', {
        body: {
          userId: userId,
          updates: updates
        }
      });

      if (updateError) throw updateError;

      if (!updateResult?.success) {
        throw new Error(updateResult?.error || 'Failed to update user');
      }

      toast.success('פרטי המשתמש עודכנו בהצלחה');
      await fetchUsers(); // Refresh the list
      return true;

    } catch (error: any) {
      console.error('Error updating user:', error);
      toast.error('שגיאה בעדכון המשתמש');
      return false;
    }
  };

  return {
    users,
    isLoading,
    isCreating,
    createUser,
    updateUser,
    updateUserRole,
    deactivateUser,
    sendPasswordReset,
    canManageUsers,
    getUserStats,
    refetch: fetchUsers
  };
};
